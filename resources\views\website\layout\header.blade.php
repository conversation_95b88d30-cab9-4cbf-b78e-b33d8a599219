{{-- Header --}}
<header class="header">
    <div class="container">
        <div class="d-flex justify-content-between align-items-end">
            <a href="{{ url('/') }}" >
            <div class="logo">
                @if(isset($settings->logo) && $settings->logo != null)
                    <img src="{{ asset($settings->logo) }}" class="img-fluid" alt="Logo">
                @else
                    <img src="{{ asset('website/assets/images/footer_logo.svg') }}" class="img-fluid" alt="Logo">
                @endif
            </div>
            </a>
            <nav class="navbar navbar-expand-lg navbar-light">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                    <span class="close_nav"><i class="fa-solid fa-close"></i></span>
                </button>

                <div class="nav_menu collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a href="{{ url('/') }}" class="nav-link">Home</a>
                        </li>
                        @if (Auth::check() && Auth::user()->roles->first()->name == 'fitter')
                            <li class="nav-item">
                                <a href="{{ url('sweetSpot') }}" class="nav-link">Sweet Spot </a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url('order-club') }}" class="nav-link">Order Clubs</a>
                            </li>
                        @elseif(Auth::check() && Auth::user()->roles->first()->name == 'customer')
                            <li class="nav-item">
                                <a href="{{ url('#') }}" class="nav-link">Order Clubs</a>
                            </li>
                        @else
                            <li class="nav-item">
                                <a href="{{ url('about-us') }}" class="nav-link">About Us</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url('products') }}" class="nav-link">Products</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url('hg-process') }}" class="nav-link">
                                    H-G Process
                                </a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="javascript:void(0)" class="nav-link dropdown-toggle" id="productsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    Fitter
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="productsDropdown">
                                    <li><a class="dropdown-item" href="{{ url('fitter') }}">Fitter</a></li>
                                    <li><a class="dropdown-item" href="{{ url('locate-fitter') }}">Locate Fitter</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url('contact-us') }}" class="nav-link">Contact Us</a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">Videos</a>
                            </li>
                            <li class="nav-item">
                                <a href="#" class="nav-link">Blogs</a>
                            </li>
                        @endif
                    </ul>
                    <a href="#" class="cart_icon" data-bs-toggle="offcanvas" data-bs-target="#offcanvasCart" aria-controls="offcanvasCart">
                        <i class="fas fa-shopping-cart"></i>
                    </a>
                    @if (!Auth::check())
                        <a href="{{ url('login') }}"  class="btn btn-primary btn-outline_primary">
                            Login <i class="fas fa-arrow-right"></i>
                        </a>
                    @else
                        @if(Auth::user()->roles->first()->name == 'fitter')
                            <div class="image-wrapper">
                                <img class="img-fluid" style="width: 45px; height: 45px;border-radius: 50%;object-fit: cover;" src="{{asset('website')}}/{{auth()->user()->profile->pic}}" alt="">
                            </div>
                            <ul class="navbar-nav ms-auto">
                                <li class="nav-item dropdown">
                                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" id="productsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ucwords(Auth::user()->name)}}
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="productsDropdown">
                                        <li><a class="dropdown-item" href="{{ url('users/show') }}">Profile</a></li>
                                        <li><a class="dropdown-item" href="{{url('logout')}}">Logout</a></li>
                                    </ul>
                                </li>
                            </ul>
                        @elseif(Auth::user()->roles->first()->name == 'customer')
                            <ul class="navbar-nav ms-auto">
                                <li class="nav-item dropdown">
                                    <a href="javascript:void(0)" class="nav-link dropdown-toggle" id="productsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{ucwords(Auth::user()->name)}}
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="productsDropdown">
                                        <li><a class="dropdown-item" href="{{url('logout')}}">Logout</a></li>
                                    </ul>
                                </li>
                            </ul>
                        @else
                            <a href="{{ route('home') }}" class="btn btn-primary btn-outline_primary">
                                Dashboard <i class="fas fa-arrow-right"></i>
                            </a>
                        @endif
                    @endif
                </div>
            </nav>
        </div>
    </div>
</header>

@push("js")
    <script>
        $(document).ready(function (){
            $('body').on('click', function (event) {
                if ($('.nav_menu').hasClass('show')) {
                    if (!$(event.target).closest('.nav_menu').length && !$(event.target).closest('.navbar-toggler').length) {
                        $('.nav_menu').removeClass('show');
                        $('.navbar-toggler').show();
                    }
                }
            });
            $('.header .navbar .nav_menu .cart_icon').click(function() {
                $('.nav_menu').removeClass('show');
            });
            $('.nav_menu').on('click', function (event) {
                event.stopPropagation();
            });
        })
    </script>
@endpush
