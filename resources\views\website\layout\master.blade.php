<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords" content="">
    <meta name="author" content="">
    <meta name="csrf-token" content="{{csrf_token()}}">
    <meta name="description" content="{{$settings->description??''}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset($settings->favicon) }}">
    <title>{{$settings->title??''}}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{{ asset('website/assets/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('website/assets/css/responsive.css') }}">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    @stack('css')

</head>

<body>

@include('website.layout.header')
@yield('content')
<div class="offcanvas offcanvas-end cart_modal" tabindex="-1" id="offcanvasCart" aria-labelledby="offcanvasCartLabel">
    <div class="offcanvas-header">
        <h5 id="">Shopping Cart</h5>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <div class="modal_cart_products">
            <div class="cart-item">
                <img class="img-fluid" src="{{asset('website')}}/assets/images/Paste1.png" alt="">
                <div>
                    <p>HENRY-GRIFFITTS- ClubGlider Meridian Travel Bag</p>
                    <p class="price">$50.00</p>
                </div>
            </div>
            <div class="cart-item">
                <img class="img-fluid" src="{{asset('website')}}/assets/images/Paste1.png" alt="">
                <div>
                    <p>HENRY-GRIFFITTS- ClubGlider Meridian Travel Bag</p>
                    <p class="price">$50.00</p>
                </div>
            </div>
        </div>
        <div class="modal_total_box">
            <div class="detail_value sub_total">
                <p>Sub Total</p>
                <p>$100.00</p>
            </div>
            <div class="detail_value shipping">
                <p>Shipping</p>
                <p>Free</p>
            </div>
            <div class="detail_value total">
                <p>Total</p>
                <p class="free">$100.00</p>
            </div>
        </div>
        <div class="view_more_btn light">
            <a href="{{ url('cart') }}"  class="btn btn-primary btn-sort">
                Proceed to Cart
            </a>
        </div>
    </div>
</div>
@include('website.layout.footer')

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>

@if(request()->is("order-club") )
    <script src="https://maps.googleapis.com/maps/api/js?key={{env('GOOGLE_MAP_API_KEY')}}&libraries=places&callback=initAutocomplete" async defer></script>
@endif

<script>
    $(document).ready(function() {
        function appendSpans(selector) {
            $(selector).each(function() {
                let newDiv = $('<div class="inner_span_div"></div>');
                $(this).append(newDiv);

                for (let i = 0; i < 5; i++) {
                    newDiv.append('<span></span>');
                }
            });
        }

        appendSpans('.light_btn a');
        appendSpans('.light_btn_btn a');
        appendSpans('.rounded_btn a');
    });
</script>
@stack('js')
@if(session()->has('flash_message'))
    <script>
        swal({
            icon: 'success',
            title: "{{ session('flash_message') }}",
            showConfirmButton: false,
            timer: 4500
        });
    </script>
@endif
@if(session()->has('message'))
    <script>
        swal({
            icon: "{{ session('type') }}",
            title: "{{ session('title') }}",
            text: "{{ session('message') }}",
            showConfirmButton: false,
            timer: 4500
        });
    </script>
@endif
</body>
</html>
